{"name": "frontend", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@paypal/react-paypal-js": "^7.8.2", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "bootstrap": "^5.2.3", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-icons": "^4.8.0", "react-redux": "^8.0.5", "react-router-dom": "^6.8.2", "react-scripts": "5.0.1", "react-toastify": "^9.1.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}